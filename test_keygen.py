#!/usr/bin/env python3
"""
Test script for MikroTik RouterOS License Key Generator
Verifies that all functionality works correctly
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from keygen import MikroTikKeygen, generate_random_software_id
    print("✓ Successfully imported keygen module")
except ImportError as e:
    print(f"✗ Failed to import keygen module: {e}")
    sys.exit(1)

def test_software_id_validation():
    """Test software ID validation"""
    print("\n--- Testing Software ID Validation ---")
    keygen = MikroTikKeygen()
    
    # Valid IDs
    valid_ids = ["TEST-1234", "ABCD-EFGH", "T0BY-X185", "HZAI-67DG"]
    for sid in valid_ids:
        if keygen.validate_software_id(sid):
            print(f"✓ {sid} is valid")
        else:
            print(f"✗ {sid} should be valid but validation failed")
    
    # Invalid IDs
    invalid_ids = ["TEST-123", "ABCD-EFGHI", "test-1234", "ABCD_EFGH", "ABCD-EFG@"]
    for sid in invalid_ids:
        if not keygen.validate_software_id(sid):
            print(f"✓ {sid} correctly identified as invalid")
        else:
            print(f"✗ {sid} should be invalid but validation passed")

def test_random_generation():
    """Test random software ID generation"""
    print("\n--- Testing Random Software ID Generation ---")
    try:
        for i in range(5):
            random_id = generate_random_software_id()
            keygen = MikroTikKeygen()
            if keygen.validate_software_id(random_id):
                print(f"✓ Generated valid random ID: {random_id}")
            else:
                print(f"✗ Generated invalid random ID: {random_id}")
    except Exception as e:
        print(f"✗ Error generating random IDs: {e}")

def test_license_generation():
    """Test license key generation"""
    print("\n--- Testing License Generation ---")
    keygen = MikroTikKeygen()
    
    test_cases = [
        ("TEST-1234", 6, None),
        ("ABCD-EFGH", 4, ["system", "dhcp"]),
        ("T0BY-X185", 6, ["system", "dhcp", "wireless", "routing"]),
    ]
    
    for software_id, level, features in test_cases:
        try:
            license_key = keygen.generate_license(software_id, level, features)
            if license_key and len(license_key) > 50:  # Basic sanity check
                print(f"✓ Generated license for {software_id} (level {level})")
                print(f"  License length: {len(license_key)} characters")
            else:
                print(f"✗ Failed to generate license for {software_id}")
        except Exception as e:
            print(f"✗ Error generating license for {software_id}: {e}")

def test_feature_combinations():
    """Test different feature combinations"""
    print("\n--- Testing Feature Combinations ---")
    keygen = MikroTikKeygen()
    
    feature_sets = [
        [],  # Default features
        ["system"],
        ["system", "dhcp", "wireless"],
        ["system", "dhcp", "wireless", "routing", "ppp", "hotspot"],
        ["system", "dhcp", "wireless", "routing", "ppp", "hotspot", "mpls", "advanced-tools"],
    ]
    
    for i, features in enumerate(feature_sets):
        try:
            license_key = keygen.generate_license("TEST-1234", 6, features)
            if license_key:
                feature_desc = f"{len(features)} features" if features else "default features"
                print(f"✓ Generated license with {feature_desc}")
            else:
                print(f"✗ Failed to generate license with features: {features}")
        except Exception as e:
            print(f"✗ Error with features {features}: {e}")

def test_license_levels():
    """Test different license levels"""
    print("\n--- Testing License Levels ---")
    keygen = MikroTikKeygen()
    
    for level in range(1, 7):
        try:
            license_key = keygen.generate_license("TEST-1234", level)
            if license_key:
                print(f"✓ Generated license for level {level}")
            else:
                print(f"✗ Failed to generate license for level {level}")
        except Exception as e:
            print(f"✗ Error generating license for level {level}: {e}")

def main():
    """Run all tests"""
    print("MikroTik RouterOS License Key Generator - Test Suite")
    print("=" * 55)
    
    try:
        test_software_id_validation()
        test_random_generation()
        test_license_generation()
        test_feature_combinations()
        test_license_levels()
        
        print("\n" + "=" * 55)
        print("Test suite completed!")
        print("If you see mostly ✓ marks above, the keygen is working correctly.")
        print("Any ✗ marks indicate potential issues that should be investigated.")
        
    except Exception as e:
        print(f"\n✗ Test suite failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
