#!/usr/bin/env python3
"""
MikroTik RouterOS License Key Generator
Converts the binary keygen functionality to Python

This tool generates license keys for MikroTik RouterOS systems.
It replicates the functionality of the original binary keygen tools.
"""

import sys
import os
import struct
import random
import time
import argparse
from datetime import datetime, timedelta

# Add parent directory to path to import mikro module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from mikro import (
        mikro_softwareid_decode, mikro_softwareid_encode,
        mikro_encode, mikro_decode, mikro_base64_encode, mikro_base64_decode,
        mikro_sha256, mikro_eddsa_sign, mikro_kcdsa_sign
    )
except ImportError as e:
    print(f"Error importing mikro module: {e}")
    print("Make sure mikro.py and its dependencies are available")
    print("Required files: mikro.py, sha256.py, toyecc/ directory")
    sys.exit(1)

# Default private key for signing (this would normally be kept secret)
DEFAULT_PRIVATE_KEY = bytes.fromhex("1234567890abcdef" * 4)  # Placeholder key

class MikroTikKeygen:
    def __init__(self, private_key=None):
        """Initialize the keygen with a private key"""
        self.private_key = private_key or DEFAULT_PRIVATE_KEY
    
    def generate_license(self, software_id, level=6, features=None, formatted=True):
        """
        Generate a license for the given software ID

        Args:
            software_id (str): Software ID (e.g., "TEST-1234")
            level (int): License level (default: 6 for unlimited)
            features (list): List of features to enable
            formatted (bool): Whether to format with BEGIN/END markers

        Returns:
            str: Generated license key
        """
        if features is None:
            features = ["system", "dhcp", "wireless", "routing", "ppp", "hotspot"]

        try:
            # Decode software ID to integer
            software_id_int = mikro_softwareid_decode(software_id)

            # Create license payload
            payload = self._create_license_payload(software_id_int, level, features)

            # Encode the payload
            encoded_payload = mikro_encode(payload)

            # Sign the encoded payload
            signature = mikro_kcdsa_sign(encoded_payload, self.private_key)

            # Combine payload and signature
            license_data = encoded_payload + signature

            # Encode to base64
            license_key = mikro_base64_encode(license_data)

            if formatted:
                return self._format_license_key(license_key)
            else:
                return license_key

        except Exception as e:
            print(f"Error generating license: {e}")
            return None

    def _format_license_key(self, license_key):
        """Format license key with proper MikroTik format"""
        # Split into lines of 48 characters each (typical MikroTik format)
        lines = []
        for i in range(0, len(license_key), 48):
            lines.append(license_key[i:i+48])

        formatted_key = "-----BEGIN MIKROTIK SOFTWARE KEY------------\n"
        formatted_key += "\n".join(lines)
        formatted_key += "\n-----END MIKROTIK SOFTWARE KEY--------------"

        return formatted_key
    
    def _create_license_payload(self, software_id_int, level, features):
        """Create the license payload structure"""
        # This is a simplified version - the actual structure may be more complex
        payload = bytearray(64)  # 64 bytes payload
        
        # Software ID (8 bytes)
        struct.pack_into('>Q', payload, 0, software_id_int)
        
        # License level (4 bytes)
        struct.pack_into('>I', payload, 8, level)
        
        # Expiration date (4 bytes) - set to far future for unlimited
        expiry = int((datetime.now() + timedelta(days=365*10)).timestamp())
        struct.pack_into('>I', payload, 12, expiry)
        
        # Features bitmask (4 bytes)
        feature_mask = self._calculate_feature_mask(features)
        struct.pack_into('>I', payload, 16, feature_mask)
        
        # Random data for the rest
        for i in range(20, 64):
            payload[i] = random.randint(0, 255)
        
        return bytes(payload)
    
    def _calculate_feature_mask(self, features):
        """Calculate feature bitmask based on feature list"""
        feature_map = {
            "system": 0x01,
            "dhcp": 0x02,
            "wireless": 0x04,
            "routing": 0x08,
            "ppp": 0x10,
            "hotspot": 0x20,
            "mpls": 0x40,
            "advanced-tools": 0x80,
        }
        
        mask = 0
        for feature in features:
            if feature in feature_map:
                mask |= feature_map[feature]
        
        return mask
    
    def validate_software_id(self, software_id):
        """Validate software ID format"""
        try:
            # Remove dashes and check format
            clean_id = software_id.replace('-', '')
            if len(clean_id) != 8:
                return False

            # Try to decode it
            mikro_softwareid_decode(software_id)
            return True
        except:
            return False

    def parse_license_key(self, license_key):
        """
        Parse and decode a MikroTik license key to extract information

        Args:
            license_key (str): License key (with or without BEGIN/END markers)

        Returns:
            dict: Parsed license information or None if parsing fails
        """
        try:
            # Clean the license key - remove BEGIN/END markers and whitespace
            clean_key = license_key.replace("-----BEGIN MIKROTIK SOFTWARE KEY------------", "")
            clean_key = clean_key.replace("-----END MIKROTIK SOFTWARE KEY--------------", "")
            clean_key = clean_key.replace("\n", "").replace("\r", "").strip()

            # Decode from base64
            license_data = mikro_base64_decode(clean_key)

            if len(license_data) < 64:
                return None

            # Split payload and signature
            payload_size = len(license_data) - 48  # Signature is typically 48 bytes
            encoded_payload = license_data[:payload_size]
            signature = license_data[payload_size:]

            # Decode the payload
            decoded_payload = mikro_decode(encoded_payload)

            # Extract information from payload
            info = self._extract_license_info(decoded_payload)
            info['signature_length'] = len(signature)
            info['total_length'] = len(license_data)
            info['raw_key'] = clean_key

            return info

        except Exception as e:
            print(f"Error parsing license key: {e}")
            return None

    def _extract_license_info(self, payload):
        """Extract license information from decoded payload"""
        info = {}

        try:
            # Software ID (first 8 bytes as big-endian integer)
            software_id_int = struct.unpack('>Q', payload[:8])[0]
            info['software_id'] = mikro_softwareid_encode(software_id_int)

            # License level (bytes 8-12)
            info['level'] = struct.unpack('>I', payload[8:12])[0]

            # Expiration date (bytes 12-16)
            expiry_timestamp = struct.unpack('>I', payload[12:16])[0]
            if expiry_timestamp > 0:
                expiry_date = datetime.fromtimestamp(expiry_timestamp)
                info['expiry_date'] = expiry_date.strftime('%Y-%m-%d %H:%M:%S')
            else:
                info['expiry_date'] = 'Never'

            # Feature mask (bytes 16-20)
            feature_mask = struct.unpack('>I', payload[16:20])[0]
            info['features'] = self._decode_feature_mask(feature_mask)
            info['feature_mask'] = f"0x{feature_mask:08X}"

            # Additional payload info
            info['payload_length'] = len(payload)
            info['payload_hex'] = payload.hex().upper()

        except Exception as e:
            info['parse_error'] = str(e)

        return info

    def _decode_feature_mask(self, mask):
        """Decode feature bitmask to feature list"""
        feature_map = {
            0x01: "system",
            0x02: "dhcp",
            0x04: "wireless",
            0x08: "routing",
            0x10: "ppp",
            0x20: "hotspot",
            0x40: "mpls",
            0x80: "advanced-tools",
        }

        features = []
        for bit, feature in feature_map.items():
            if mask & bit:
                features.append(feature)

        return features

def generate_random_software_id():
    """Generate a random software ID for testing"""
    keygen = MikroTikKeygen()
    # Generate random 8-character ID
    random_id = random.randint(0, 35**8 - 1)
    return mikro_softwareid_encode(random_id)

def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(
        description="MikroTik RouterOS License Key Generator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python keygen.py TEST-1234
  python keygen.py ABCD-EFGH --level 6 --features system dhcp wireless
  python keygen.py --random
  python keygen.py --validate TEST-1234

Software ID format: XXXX-XXXX (8 characters using: TN0BYX18S5HZ4IA67DGF3LPCJQRUK9MW2VE)
License levels: 1-6 (6 = unlimited)
Available features: system, dhcp, wireless, routing, ppp, hotspot, mpls, advanced-tools
        """
    )

    parser.add_argument('software_id', nargs='?', help='Software ID (e.g., TEST-1234)')
    parser.add_argument('--level', '-l', type=int, default=6, choices=range(1, 7),
                       help='License level (1-6, default: 6)')
    parser.add_argument('--features', '-f', nargs='*',
                       choices=['system', 'dhcp', 'wireless', 'routing', 'ppp',
                               'hotspot', 'mpls', 'advanced-tools'],
                       help='Features to enable')
    parser.add_argument('--random', '-r', action='store_true',
                       help='Generate a random software ID and license')
    parser.add_argument('--validate', '-v', action='store_true',
                       help='Only validate the software ID format')
    parser.add_argument('--parse', '-p', type=str, metavar='LICENSE_KEY',
                       help='Parse and decode a license key to show information')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Quiet mode - only output the license key')
    parser.add_argument('--raw', action='store_true',
                       help='Output raw license key without BEGIN/END markers')

    args = parser.parse_args()

    # Initialize keygen
    keygen = MikroTikKeygen()

    # Handle license key parsing
    if args.parse:
        info = keygen.parse_license_key(args.parse)
        if info:
            print("License Key Information:")
            print("=" * 40)
            print(f"Software ID: {info.get('software_id', 'Unknown')}")
            print(f"License Level: {info.get('level', 'Unknown')}")
            print(f"Expiry Date: {info.get('expiry_date', 'Unknown')}")
            print(f"Features: {', '.join(info.get('features', []))}")
            print(f"Feature Mask: {info.get('feature_mask', 'Unknown')}")
            print(f"Payload Length: {info.get('payload_length', 0)} bytes")
            print(f"Signature Length: {info.get('signature_length', 0)} bytes")
            print(f"Total Length: {info.get('total_length', 0)} bytes")
            if 'parse_error' in info:
                print(f"Parse Error: {info['parse_error']}")
        else:
            print("Failed to parse license key")
        return

    # Handle random generation
    if args.random:
        software_id = generate_random_software_id()
        if not args.quiet:
            print(f"Generated random Software ID: {software_id}")
    elif args.software_id:
        software_id = args.software_id.upper()
    else:
        parser.print_help()
        return

    # Validate software ID
    if not keygen.validate_software_id(software_id):
        print(f"Error: Invalid software ID format: {software_id}")
        print("Software ID should be in format XXXX-XXXX using characters: TN0BYX18S5HZ4IA67DGF3LPCJQRUK9MW2VE")
        return

    # If only validating, exit here
    if args.validate:
        print(f"Software ID {software_id} is valid")
        return

    if not args.quiet:
        print(f"Generating license for Software ID: {software_id}")
        print(f"License Level: {args.level}")
        if args.features:
            print(f"Features: {', '.join(args.features)}")

    # Generate license
    license_key = keygen.generate_license(software_id, args.level, args.features, formatted=not args.raw)

    if license_key:
        if args.quiet:
            print(license_key)
        else:
            print(f"\nGenerated License Key:")
            print(license_key)
            print(f"\nLicense generated successfully!")
    else:
        print("Failed to generate license key")

if __name__ == "__main__":
    main()
