#!/usr/bin/env python3
"""
Advanced license key analyzer for MikroTik licenses
Provides detailed analysis of the license structure
"""

import sys
import os
import base64
import struct
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from mikro import mikro_base64_decode, mikro_decode, mikro_softwareid_encode
except ImportError:
    print("Warning: Could not import mikro functions, using standard base64")
    def mikro_base64_decode(data):
        return base64.b64decode(data)

def analyze_license_raw(license_key):
    """Analyze license key at byte level"""
    print("RAW ANALYSIS")
    print("=" * 50)
    
    # Clean the license key
    clean_key = license_key.replace("-----BEGIN MIKROTIK SOFTWARE KEY------------", "")
    clean_key = clean_key.replace("-----E<PERSON> MIKROTIK SOFTWARE KEY--------------", "")
    clean_key = clean_key.replace("\n", "").replace("\r", "").strip()
    
    print(f"Clean base64 key: {clean_key}")
    print(f"Base64 length: {len(clean_key)} characters")
    
    try:
        # Decode from base64
        license_data = base64.b64decode(clean_key)
        print(f"Decoded length: {len(license_data)} bytes")
        print(f"Decoded hex: {license_data.hex().upper()}")
        
        # Analyze structure
        print("\nSTRUCTURE ANALYSIS")
        print("-" * 30)
        
        # Try different interpretations
        if len(license_data) >= 8:
            # First 8 bytes as different formats
            first_8 = license_data[:8]
            print(f"First 8 bytes (hex): {first_8.hex().upper()}")
            print(f"First 8 bytes (big-endian uint64): {struct.unpack('>Q', first_8)[0]}")
            print(f"First 8 bytes (little-endian uint64): {struct.unpack('<Q', first_8)[0]}")
            
            # Try to decode as software ID
            try:
                software_id = mikro_softwareid_encode(struct.unpack('>Q', first_8)[0])
                print(f"As Software ID (big-endian): {software_id}")
            except:
                print("Could not decode as software ID (big-endian)")
            
            try:
                software_id = mikro_softwareid_encode(struct.unpack('<Q', first_8)[0])
                print(f"As Software ID (little-endian): {software_id}")
            except:
                print("Could not decode as software ID (little-endian)")
        
        if len(license_data) >= 12:
            # Next 4 bytes as level
            level_bytes = license_data[8:12]
            print(f"Bytes 8-12 (hex): {level_bytes.hex().upper()}")
            print(f"Bytes 8-12 (big-endian uint32): {struct.unpack('>I', level_bytes)[0]}")
            print(f"Bytes 8-12 (little-endian uint32): {struct.unpack('<I', level_bytes)[0]}")
        
        if len(license_data) >= 16:
            # Next 4 bytes as timestamp
            time_bytes = license_data[12:16]
            print(f"Bytes 12-16 (hex): {time_bytes.hex().upper()}")
            timestamp_be = struct.unpack('>I', time_bytes)[0]
            timestamp_le = struct.unpack('<I', time_bytes)[0]
            print(f"Bytes 12-16 (big-endian uint32): {timestamp_be}")
            print(f"Bytes 12-16 (little-endian uint32): {timestamp_le}")
            
            # Try to interpret as timestamps
            try:
                if timestamp_be > 0:
                    date_be = datetime.fromtimestamp(timestamp_be)
                    print(f"As timestamp (big-endian): {date_be}")
            except:
                pass
            
            try:
                if timestamp_le > 0:
                    date_le = datetime.fromtimestamp(timestamp_le)
                    print(f"As timestamp (little-endian): {date_le}")
            except:
                pass
        
        # Show all bytes in groups
        print(f"\nBYTE BREAKDOWN")
        print("-" * 30)
        for i in range(0, len(license_data), 16):
            chunk = license_data[i:i+16]
            hex_str = ' '.join(f'{b:02X}' for b in chunk)
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
            print(f"{i:04X}: {hex_str:<48} {ascii_str}")
        
        # Try MikroTik specific decoding
        print(f"\nMIKROTIK DECODING ATTEMPT")
        print("-" * 30)
        try:
            decoded = mikro_decode(license_data)
            print(f"MikroTik decode successful: {len(decoded)} bytes")
            print(f"Decoded hex: {decoded.hex().upper()}")
        except Exception as e:
            print(f"MikroTik decode failed: {e}")
        
        return license_data
        
    except Exception as e:
        print(f"Error decoding base64: {e}")
        return None

def main():
    license_key = """-----BEGIN MIKROTIK SOFTWARE KEY------------
uaSG53vbUuXz9B4uP47OuzyfBwlImqD4x/PqXUbK+g4L
7pQAxT/8WN2jnU2FGKSQ5x/J64bSS9AbfbfslX3xHA==
-----END MIKROTIK SOFTWARE KEY--------------"""

    print("MikroTik License Key Advanced Analysis")
    print("=" * 60)
    print("Analyzing license key:")
    print(license_key)
    print("\n" + "=" * 60)
    
    license_data = analyze_license_raw(license_key)
    
    if license_data:
        print(f"\nSUMMARY")
        print("-" * 30)
        print(f"Total license data: {len(license_data)} bytes")
        print(f"This appears to be a {len(license_data)*8}-bit license structure")
        
        # Common MikroTik license sizes
        if len(license_data) == 64:
            print("This matches a typical MikroTik license size (64 bytes)")
        elif len(license_data) == 48:
            print("This might be a signature-only structure")
        else:
            print(f"Unusual license size: {len(license_data)} bytes")

if __name__ == "__main__":
    main()
