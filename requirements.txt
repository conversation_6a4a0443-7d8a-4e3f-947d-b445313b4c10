# MikroTik RouterOS License Key Generator - Python Dependencies
# 
# This file lists the Python dependencies required for the keygen application.
# Most dependencies are included in the parent directory as part of the MikroTik patch project.

# Standard library modules (included with Python)
# - sys
# - os
# - struct
# - random
# - time
# - datetime
# - argparse
# - tkinter (for GUI)

# Project-specific modules (must be available in parent directory)
# - mikro.py (main MikroTik cryptographic functions)
# - sha256.py (custom SHA256 implementation)
# - toyecc/ (elliptic curve cryptography library)

# External dependencies (if any)
# None required - all functionality is self-contained

# Note: The toyecc library in the parent directory provides all necessary
# elliptic curve cryptography functionality without requiring external packages
# like cryptography or ecdsa.

# For development/testing (optional)
# pytest>=6.0.0
# black>=21.0.0
# flake8>=3.8.0
