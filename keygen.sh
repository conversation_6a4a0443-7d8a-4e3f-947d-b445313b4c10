#!/bin/bash
# MikroTik RouterOS License Key Generator - Shell Script
# This script provides an easy way to run the Python keygen

echo "MikroTik RouterOS License Key Generator"
echo "====================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "Error: Python is not installed or not in PATH"
        echo "Please install Python 3.6 or later"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Check if keygen.py exists
if [ ! -f "keygen.py" ]; then
    echo "Error: keygen.py not found in current directory"
    exit 1
fi

# If no arguments provided, show help
if [ $# -eq 0 ]; then
    $PYTHON_CMD keygen.py --help
    echo ""
    echo "Examples:"
    echo "  ./keygen.sh TEST-1234"
    echo "  ./keygen.sh --random"
    echo "  ./keygen.sh --gui"
    echo ""
    exit 0
fi

# Special case for GUI
if [ "$1" = "--gui" ]; then
    echo "Starting GUI version..."
    $PYTHON_CMD keygen_gui.py
    exit 0
fi

# Run the keygen with all arguments
$PYTHON_CMD keygen.py "$@"
