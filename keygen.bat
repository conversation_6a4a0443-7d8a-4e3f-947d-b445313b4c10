@echo off
REM MikroTik RouterOS License Key Generator - Windows Batch Script
REM This script provides an easy way to run the Python keygen

echo MikroTik RouterOS License Key Generator
echo =====================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.6 or later
    pause
    exit /b 1
)

REM Check if keygen.py exists
if not exist "keygen.py" (
    echo Error: keygen.py not found in current directory
    pause
    exit /b 1
)

REM If no arguments provided, show help
if "%1"=="" (
    python keygen.py --help
    echo.
    echo Examples:
    echo   keygen.bat TEST-1234
    echo   keygen.bat --random
    echo   keygen.bat --gui
    echo.
    pause
    exit /b 0
)

REM Special case for GUI
if "%1"=="--gui" (
    echo Starting GUI version...
    python keygen_gui.py
    exit /b 0
)

REM Run the keygen with all arguments
python keygen.py %*

pause
