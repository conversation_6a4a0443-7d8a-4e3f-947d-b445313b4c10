#!/usr/bin/env python3
"""
MikroTik RouterOS License Key Generator - GUI Version
Simple graphical interface for the keygen functionality
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import random

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from keygen import MikroTikKeygen, generate_random_software_id
except ImportError as e:
    print(f"Error importing keygen module: {e}")
    print("Make sure keygen.py is in the same directory")
    sys.exit(1)

class KeygenGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("MikroTik RouterOS License Key Generator")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # Initialize keygen
        self.keygen = MikroTikKeygen()
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="MikroTik RouterOS License Key Generator", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Software ID section
        ttk.Label(main_frame, text="Software ID:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.software_id_var = tk.StringVar()
        software_id_entry = ttk.Entry(main_frame, textvariable=self.software_id_var, width=20)
        software_id_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        random_btn = ttk.Button(main_frame, text="Random", command=self.generate_random_id)
        random_btn.grid(row=1, column=2, pady=5, padx=(5, 0))
        
        # License level section
        ttk.Label(main_frame, text="License Level:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.level_var = tk.IntVar(value=6)
        level_combo = ttk.Combobox(main_frame, textvariable=self.level_var, 
                                  values=[1, 2, 3, 4, 5, 6], state="readonly", width=10)
        level_combo.grid(row=2, column=1, sticky=tk.W, pady=5, padx=(5, 0))
        
        # Features section
        ttk.Label(main_frame, text="Features:").grid(row=3, column=0, sticky=(tk.W, tk.N), pady=5)
        
        features_frame = ttk.Frame(main_frame)
        features_frame.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        self.feature_vars = {}
        features = ["system", "dhcp", "wireless", "routing", "ppp", "hotspot", "mpls", "advanced-tools"]
        default_features = ["system", "dhcp", "wireless", "routing", "ppp", "hotspot"]
        
        for i, feature in enumerate(features):
            var = tk.BooleanVar(value=feature in default_features)
            self.feature_vars[feature] = var
            cb = ttk.Checkbutton(features_frame, text=feature, variable=var)
            cb.grid(row=i//2, column=i%2, sticky=tk.W, padx=(0, 20), pady=2)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=20)
        
        generate_btn = ttk.Button(button_frame, text="Generate License", 
                                 command=self.generate_license, style="Accent.TButton")
        generate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        validate_btn = ttk.Button(button_frame, text="Validate ID", 
                                 command=self.validate_id)
        validate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = ttk.Button(button_frame, text="Clear", command=self.clear_all)
        clear_btn.pack(side=tk.LEFT)
        
        # Output section
        ttk.Label(main_frame, text="Generated License:").grid(row=5, column=0, sticky=(tk.W, tk.N), pady=(20, 5))
        
        self.output_text = scrolledtext.ScrolledText(main_frame, height=8, width=70, wrap=tk.WORD)
        self.output_text.grid(row=5, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), 
                             pady=(20, 5), padx=(5, 0))
        
        # Copy button
        copy_btn = ttk.Button(main_frame, text="Copy to Clipboard", command=self.copy_to_clipboard)
        copy_btn.grid(row=6, column=1, pady=5, padx=(5, 0), sticky=tk.W)
        
        # Configure grid weights for resizing
        main_frame.rowconfigure(5, weight=1)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def generate_random_id(self):
        """Generate a random software ID"""
        try:
            random_id = generate_random_software_id()
            self.software_id_var.set(random_id)
            self.status_var.set(f"Generated random Software ID: {random_id}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate random ID: {e}")
    
    def validate_id(self):
        """Validate the software ID"""
        software_id = self.software_id_var.get().strip().upper()
        if not software_id:
            messagebox.showwarning("Warning", "Please enter a Software ID")
            return
        
        if self.keygen.validate_software_id(software_id):
            messagebox.showinfo("Valid", f"Software ID '{software_id}' is valid")
            self.status_var.set(f"Software ID '{software_id}' is valid")
        else:
            messagebox.showerror("Invalid", f"Software ID '{software_id}' is invalid")
            self.status_var.set(f"Software ID '{software_id}' is invalid")
    
    def generate_license(self):
        """Generate a license key"""
        software_id = self.software_id_var.get().strip().upper()
        if not software_id:
            messagebox.showwarning("Warning", "Please enter a Software ID")
            return
        
        if not self.keygen.validate_software_id(software_id):
            messagebox.showerror("Error", f"Invalid Software ID format: {software_id}")
            return
        
        # Get selected features
        selected_features = [feature for feature, var in self.feature_vars.items() if var.get()]
        
        try:
            self.status_var.set("Generating license...")
            self.root.update()
            
            license_key = self.keygen.generate_license(
                software_id, 
                self.level_var.get(), 
                selected_features if selected_features else None
            )
            
            if license_key:
                self.output_text.delete(1.0, tk.END)
                self.output_text.insert(tk.END, f"Software ID: {software_id}\n")
                self.output_text.insert(tk.END, f"License Level: {self.level_var.get()}\n")
                self.output_text.insert(tk.END, f"Features: {', '.join(selected_features)}\n\n")
                self.output_text.insert(tk.END, f"License Key:\n{license_key}")
                self.status_var.set("License generated successfully!")
            else:
                messagebox.showerror("Error", "Failed to generate license key")
                self.status_var.set("Failed to generate license")
                
        except Exception as e:
            messagebox.showerror("Error", f"Error generating license: {e}")
            self.status_var.set(f"Error: {e}")
    
    def copy_to_clipboard(self):
        """Copy the license key to clipboard"""
        content = self.output_text.get(1.0, tk.END).strip()
        if content:
            self.root.clipboard_clear()
            self.root.clipboard_append(content)
            self.status_var.set("Copied to clipboard!")
        else:
            messagebox.showwarning("Warning", "No license to copy")
    
    def clear_all(self):
        """Clear all fields"""
        self.software_id_var.set("")
        self.level_var.set(6)
        for var in self.feature_vars.values():
            var.set(False)
        # Set default features
        default_features = ["system", "dhcp", "wireless", "routing", "ppp", "hotspot"]
        for feature in default_features:
            if feature in self.feature_vars:
                self.feature_vars[feature].set(True)
        self.output_text.delete(1.0, tk.END)
        self.status_var.set("Cleared")

def main():
    """Main function"""
    root = tk.Tk()
    
    # Set theme
    try:
        root.tk.call("source", "azure.tcl")
        root.tk.call("set_theme", "light")
    except:
        pass  # Theme not available, use default
    
    app = KeygenGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
