#!/usr/bin/env python3
"""
MikroTik License Reconstructor
Attempts to create valid licenses based on reverse engineering analysis
"""

import sys
import os
import base64
import struct
import hashlib
import hmac
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from mikro import (
        mikro_base64_decode, mikro_base64_encode, mikro_decode, mikro_encode,
        mikro_softwareid_decode, mikro_softwareid_encode, mikro_sha256,
        mikro_kcdsa_sign, mikro_eddsa_sign
    )
    MIKRO_AVAILABLE = True
except ImportError:
    print("Warning: MikroTik modules not available")
    MIKRO_AVAILABLE = False

class LicenseReconstructor:
    def __init__(self):
        self.target_software_id = '99FB-YFSC'
        self.target_level = 6  # Upgrade to level 6
        self.target_version = 7
        
        # Known patterns from analysis
        self.known_nonce = bytes.fromhex('2c7f015c22a63ae0f1ffa817b5293e88')
        self.known_signature = bytes.fromhex('2f7b0a01f1f4f356638f27651786224179fc273abe49520f6cdff6b1e575c707')
        
    def create_license_payload(self):
        """Create the license payload structure"""
        print("CREATING LICENSE PAYLOAD")
        print("=" * 40)
        
        if not MIKRO_AVAILABLE:
            print("MikroTik functions not available")
            return None
        
        try:
            # Encode software ID
            sw_id_int = mikro_softwareid_decode(self.target_software_id)
            print(f"Software ID: {self.target_software_id} -> {sw_id_int}")
            
            # Create payload structure based on analysis
            # Structure: [SW_ID(8)] [LEVEL(4)] [VERSION(4)] [NONCE(16)] [RESERVED(32)]
            payload = bytearray(64)
            
            # Software ID (8 bytes, big-endian)
            struct.pack_into('>Q', payload, 0, sw_id_int)
            print(f"Software ID bytes: {payload[0:8].hex().upper()}")
            
            # License level (4 bytes, big-endian)
            struct.pack_into('>I', payload, 8, self.target_level)
            print(f"Level bytes: {payload[8:12].hex().upper()}")
            
            # Version (4 bytes, big-endian)
            struct.pack_into('>I', payload, 12, self.target_version)
            print(f"Version bytes: {payload[12:16].hex().upper()}")
            
            # Nonce (16 bytes) - use known nonce pattern
            payload[16:32] = self.known_nonce
            print(f"Nonce bytes: {payload[16:32].hex().upper()}")
            
            # Reserved/padding (32 bytes) - fill with pattern
            for i in range(32, 64):
                payload[i] = 0x00
            
            print(f"Complete payload: {payload.hex().upper()}")
            return bytes(payload)
            
        except Exception as e:
            print(f"Error creating payload: {e}")
            return None
    
    def sign_payload(self, payload):
        """Attempt to sign the payload"""
        print(f"\nSIGNING PAYLOAD")
        print("=" * 40)
        
        if not MIKRO_AVAILABLE:
            print("MikroTik signing functions not available")
            return None
        
        # Try different signing methods
        signing_methods = [
            ('KCDSA', mikro_kcdsa_sign),
            ('EDDSA', mikro_eddsa_sign),
        ]
        
        # Default private key (placeholder)
        private_key = bytes.fromhex("1234567890abcdef" * 4)
        
        for method_name, sign_func in signing_methods:
            try:
                print(f"Trying {method_name} signing...")
                signature = sign_func(payload, private_key)
                print(f"{method_name} signature: {signature.hex().upper()}")
                return signature
            except Exception as e:
                print(f"{method_name} signing failed: {e}")
        
        # Fallback: use known signature pattern
        print("Using known signature pattern...")
        return self.known_signature
    
    def create_license_v1(self):
        """Create license using method 1: Direct payload + signature"""
        print(f"\nMETHOD 1: DIRECT PAYLOAD + SIGNATURE")
        print("=" * 50)
        
        payload = self.create_license_payload()
        if not payload:
            return None
        
        signature = self.sign_payload(payload)
        if not signature:
            return None
        
        # Combine payload and signature
        license_data = payload + signature[:32]  # Truncate signature to 32 bytes
        
        # Encode to base64
        license_b64 = base64.b64encode(license_data).decode()
        
        # Format as MikroTik license
        formatted_license = self.format_license(license_b64)
        
        print(f"Generated license (Method 1):")
        print(formatted_license)
        
        return formatted_license
    
    def create_license_v2(self):
        """Create license using method 2: MikroTik encoding"""
        print(f"\nMETHOD 2: MIKROTIK ENCODING")
        print("=" * 50)
        
        if not MIKRO_AVAILABLE:
            print("MikroTik encoding not available")
            return None
        
        try:
            # Create basic payload
            payload = self.create_license_payload()
            if not payload:
                return None
            
            # Encode using MikroTik encoding
            encoded_payload = mikro_encode(payload)
            print(f"MikroTik encoded payload: {encoded_payload.hex().upper()}")
            
            # Sign the encoded payload
            signature = self.sign_payload(encoded_payload)
            if not signature:
                return None
            
            # Combine encoded payload and signature
            license_data = encoded_payload + signature[:32]
            
            # Encode to base64
            license_b64 = mikro_base64_encode(license_data)
            
            # Format as MikroTik license
            formatted_license = self.format_license(license_b64)
            
            print(f"Generated license (Method 2):")
            print(formatted_license)
            
            return formatted_license
            
        except Exception as e:
            print(f"Method 2 failed: {e}")
            return None
    
    def create_license_v3(self):
        """Create license using method 3: Pattern-based reconstruction"""
        print(f"\nMETHOD 3: PATTERN-BASED RECONSTRUCTION")
        print("=" * 50)
        
        try:
            # Start with the original license structure
            original_hex = "B9A486E77BDB52E5F3F41E2E3F8ECEBB3C9F0709489AA0F8C7F3EA5D46CAFA0E0BEE9400C53FFC58DDA39D4D8518A490E71FC9EB86D24BD01B7DB7EC957DF11C"
            license_data = bytearray(bytes.fromhex(original_hex))
            
            # Replace software ID (first 8 bytes)
            if MIKRO_AVAILABLE:
                sw_id_int = mikro_softwareid_decode(self.target_software_id)
                struct.pack_into('>Q', license_data, 0, sw_id_int)
                print(f"Replaced software ID: {license_data[0:8].hex().upper()}")
            
            # Replace level (assuming it's at position 8-12)
            # Try different positions for level
            level_positions = [8, 12, 16, 20]
            for pos in level_positions:
                if pos + 4 <= len(license_data):
                    original_val = struct.unpack('>I', license_data[pos:pos+4])[0]
                    struct.pack_into('>I', license_data, pos, self.target_level)
                    print(f"Position {pos}: {original_val} -> {self.target_level}")
            
            # Encode to base64
            license_b64 = base64.b64encode(license_data).decode()
            
            # Format as MikroTik license
            formatted_license = self.format_license(license_b64)
            
            print(f"Generated license (Method 3):")
            print(formatted_license)
            
            return formatted_license
            
        except Exception as e:
            print(f"Method 3 failed: {e}")
            return None
    
    def format_license(self, license_b64):
        """Format license key with proper MikroTik format"""
        lines = []
        for i in range(0, len(license_b64), 48):
            lines.append(license_b64[i:i+48])
        
        formatted = "-----BEGIN MIKROTIK SOFTWARE KEY------------\n"
        formatted += "\n".join(lines)
        formatted += "\n-----END MIKROTIK SOFTWARE KEY--------------"
        
        return formatted
    
    def validate_license(self, license_key):
        """Attempt to validate the generated license"""
        print(f"\nVALIDATING GENERATED LICENSE")
        print("=" * 40)
        
        try:
            # Extract base64 content
            clean_key = license_key.replace("-----BEGIN MIKROTIK SOFTWARE KEY------------", "")
            clean_key = clean_key.replace("-----END MIKROTIK SOFTWARE KEY--------------", "")
            clean_key = clean_key.replace("\n", "").replace("\r", "").strip()
            
            # Decode
            license_data = base64.b64decode(clean_key)
            print(f"License data: {len(license_data)} bytes")
            
            # Basic validation
            if len(license_data) == 64:
                print("✓ Correct license size (64 bytes)")
            else:
                print(f"✗ Incorrect license size ({len(license_data)} bytes)")
            
            # Check if software ID is present
            if MIKRO_AVAILABLE:
                try:
                    sw_id_int = mikro_softwareid_decode(self.target_software_id)
                    sw_id_bytes = struct.pack('>Q', sw_id_int)
                    
                    if license_data.startswith(sw_id_bytes):
                        print("✓ Software ID found at beginning")
                    else:
                        print("✗ Software ID not found at expected position")
                        
                except Exception as e:
                    print(f"Software ID check failed: {e}")
            
            return True
            
        except Exception as e:
            print(f"Validation failed: {e}")
            return False

def main():
    """Main reconstruction function"""
    print("MIKROTIK LICENSE RECONSTRUCTOR")
    print("=" * 60)
    print(f"Target: Software ID {LicenseReconstructor().target_software_id} -> Level 6")
    print("=" * 60)
    
    reconstructor = LicenseReconstructor()
    
    # Try different methods
    methods = [
        reconstructor.create_license_v1,
        reconstructor.create_license_v2,
        reconstructor.create_license_v3,
    ]
    
    for i, method in enumerate(methods, 1):
        try:
            license_key = method()
            if license_key:
                reconstructor.validate_license(license_key)
                print(f"\n" + "="*60)
        except Exception as e:
            print(f"Method {i} failed: {e}")
    
    print(f"\nRECONSTRUCTION COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
