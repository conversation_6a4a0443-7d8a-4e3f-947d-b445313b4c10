#!/usr/bin/env python3
"""
MikroTik License Key Reverse Engineering Tool
Analyzes the structure and attempts to reconstruct/validate licenses
"""

import sys
import os
import base64
import struct
import hashlib
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from mikro import (
        mikro_base64_decode, mikro_base64_encode, mikro_decode, mikro_encode,
        mikro_softwareid_decode, mikro_softwareid_encode, mikro_sha256
    )
    MIKRO_AVAILABLE = True
except ImportError:
    print("Warning: MikroTik modules not available, using standard functions")
    MIKRO_AVAILABLE = False
    def mikro_base64_decode(data):
        return base64.b64decode(data)
    def mikro_base64_encode(data):
        return base64.b64encode(data).decode()

class MikroTikLicenseReverseEngineer:
    def __init__(self):
        self.known_license = {
            'software_id': '99FB-YFSC',
            'version': 7,
            'level': 4,
            'nonce': '2c7f015c22a63ae0f1ffa817b5293e88',
            'signature': '2f7b0a01f1f4f356638f27651786224179fc273abe49520f6cdff6b1e575c707',
            'valid': False,
            'raw_key': 'uaSG53vbUuXz9B4uP47OuzyfBwlImqD4x/PqXUbK+g4L7pQAxT/8WN2jnU2FGKSQ5x/J64bSS9AbfbfslX3xHA=='
        }
    
    def analyze_structure(self):
        """Analyze the license structure in detail"""
        print("MIKROTIK LICENSE REVERSE ENGINEERING")
        print("=" * 60)
        
        # Decode the raw license
        license_data = base64.b64decode(self.known_license['raw_key'])
        print(f"Raw license data: {len(license_data)} bytes")
        print(f"Hex: {license_data.hex().upper()}")
        
        # Known information mapping
        print(f"\nKNOWN INFORMATION:")
        print(f"Software ID: {self.known_license['software_id']}")
        print(f"Version: {self.known_license['version']}")
        print(f"Level: {self.known_license['level']}")
        print(f"Nonce: {self.known_license['nonce']}")
        print(f"Signature: {self.known_license['signature']}")
        print(f"Valid: {self.known_license['valid']}")
        
        # Convert known values to bytes for analysis
        nonce_bytes = bytes.fromhex(self.known_license['nonce'])
        signature_bytes = bytes.fromhex(self.known_license['signature'])
        
        print(f"\nNonce bytes ({len(nonce_bytes)}): {nonce_bytes.hex().upper()}")
        print(f"Signature bytes ({len(signature_bytes)}): {signature_bytes.hex().upper()}")
        
        # Try to find these patterns in the license data
        self.find_patterns_in_license(license_data, nonce_bytes, signature_bytes)
        
        # Analyze software ID encoding
        self.analyze_software_id_encoding()
        
        # Try to reconstruct the license structure
        self.reconstruct_license_structure(license_data)
        
        return license_data
    
    def find_patterns_in_license(self, license_data, nonce_bytes, signature_bytes):
        """Find known patterns within the license data"""
        print(f"\nPATTERN ANALYSIS:")
        print("-" * 40)
        
        # Look for nonce in license data
        nonce_pos = license_data.find(nonce_bytes)
        if nonce_pos >= 0:
            print(f"✓ Nonce found at position {nonce_pos}")
        else:
            print("✗ Nonce not found directly in license data")
            # Try partial matches
            for i in range(len(nonce_bytes) - 4):
                partial = nonce_bytes[i:i+4]
                pos = license_data.find(partial)
                if pos >= 0:
                    print(f"  Partial nonce match ({partial.hex()}) at position {pos}")
        
        # Look for signature in license data
        sig_pos = license_data.find(signature_bytes)
        if sig_pos >= 0:
            print(f"✓ Signature found at position {sig_pos}")
        else:
            print("✗ Signature not found directly in license data")
            # Try partial matches
            for i in range(len(signature_bytes) - 8):
                partial = signature_bytes[i:i+8]
                pos = license_data.find(partial)
                if pos >= 0:
                    print(f"  Partial signature match ({partial.hex()}) at position {pos}")
        
        # Look for software ID patterns
        if MIKRO_AVAILABLE:
            try:
                sw_id_int = mikro_softwareid_decode(self.known_license['software_id'])
                sw_id_bytes_be = struct.pack('>Q', sw_id_int)
                sw_id_bytes_le = struct.pack('<Q', sw_id_int)
                
                print(f"Software ID as integer: {sw_id_int}")
                print(f"Software ID bytes (BE): {sw_id_bytes_be.hex().upper()}")
                print(f"Software ID bytes (LE): {sw_id_bytes_le.hex().upper()}")
                
                pos_be = license_data.find(sw_id_bytes_be)
                pos_le = license_data.find(sw_id_bytes_le)
                
                if pos_be >= 0:
                    print(f"✓ Software ID (big-endian) found at position {pos_be}")
                if pos_le >= 0:
                    print(f"✓ Software ID (little-endian) found at position {pos_le}")
                    
            except Exception as e:
                print(f"Error analyzing software ID: {e}")
    
    def analyze_software_id_encoding(self):
        """Analyze how the software ID is encoded"""
        print(f"\nSOFTWARE ID ENCODING ANALYSIS:")
        print("-" * 40)
        
        if not MIKRO_AVAILABLE:
            print("MikroTik functions not available")
            return
        
        try:
            # Decode the software ID
            sw_id_int = mikro_softwareid_decode(self.known_license['software_id'])
            print(f"Software ID '{self.known_license['software_id']}' -> {sw_id_int}")
            
            # Try different encodings
            encodings = [
                ('>Q', 'big-endian 64-bit'),
                ('<Q', 'little-endian 64-bit'),
                ('>I', 'big-endian 32-bit (truncated)'),
                ('<I', 'little-endian 32-bit (truncated)'),
            ]
            
            for fmt, desc in encodings:
                try:
                    if 'I' in fmt:  # 32-bit
                        packed = struct.pack(fmt, sw_id_int & 0xFFFFFFFF)
                    else:  # 64-bit
                        packed = struct.pack(fmt, sw_id_int)
                    print(f"{desc}: {packed.hex().upper()}")
                except:
                    pass
                    
        except Exception as e:
            print(f"Error: {e}")
    
    def reconstruct_license_structure(self, license_data):
        """Attempt to reconstruct the license structure"""
        print(f"\nLICENSE STRUCTURE RECONSTRUCTION:")
        print("-" * 40)
        
        # Try MikroTik decoding if available
        if MIKRO_AVAILABLE:
            try:
                decoded = mikro_decode(license_data)
                print(f"MikroTik decoded: {len(decoded)} bytes")
                print(f"Decoded hex: {decoded.hex().upper()}")
                
                # Analyze decoded structure
                self.analyze_decoded_structure(decoded)
                
            except Exception as e:
                print(f"MikroTik decode failed: {e}")
        
        # Manual structure analysis
        print(f"\nMANUAL STRUCTURE ANALYSIS:")
        print("-" * 30)
        
        # Common license structures
        structures = [
            # Structure 1: [SW_ID(8)] [LEVEL(4)] [VERSION(4)] [NONCE(16)] [SIG(32)]
            (8, 4, 4, 16, 32),
            # Structure 2: [SW_ID(8)] [LEVEL(4)] [NONCE(16)] [SIG(36)]
            (8, 4, 16, 36),
            # Structure 3: [HEADER(16)] [PAYLOAD(16)] [SIG(32)]
            (16, 16, 32),
        ]
        
        for i, struct_def in enumerate(structures, 1):
            print(f"\nStructure {i}: {struct_def}")
            if sum(struct_def) == len(license_data):
                print("✓ Size matches!")
                self.parse_structure(license_data, struct_def)
            else:
                print(f"✗ Size mismatch (need {sum(struct_def)}, have {len(license_data)})")
    
    def analyze_decoded_structure(self, decoded_data):
        """Analyze the MikroTik decoded structure"""
        print(f"Analyzing decoded structure ({len(decoded_data)} bytes):")
        
        # Look for known patterns in decoded data
        nonce_bytes = bytes.fromhex(self.known_license['nonce'])
        signature_bytes = bytes.fromhex(self.known_license['signature'])
        
        # Check for nonce
        nonce_pos = decoded_data.find(nonce_bytes)
        if nonce_pos >= 0:
            print(f"✓ Nonce found in decoded data at position {nonce_pos}")
        
        # Check for signature
        sig_pos = decoded_data.find(signature_bytes)
        if sig_pos >= 0:
            print(f"✓ Signature found in decoded data at position {sig_pos}")
        
        # Try to find software ID
        if MIKRO_AVAILABLE:
            try:
                sw_id_int = mikro_softwareid_decode(self.known_license['software_id'])
                sw_id_bytes = struct.pack('>Q', sw_id_int)
                sw_id_pos = decoded_data.find(sw_id_bytes)
                if sw_id_pos >= 0:
                    print(f"✓ Software ID found in decoded data at position {sw_id_pos}")
            except:
                pass
    
    def parse_structure(self, data, structure):
        """Parse data according to given structure"""
        offset = 0
        for i, size in enumerate(structure):
            chunk = data[offset:offset+size]
            print(f"  Field {i+1} ({size} bytes): {chunk.hex().upper()}")
            
            # Try to interpret the chunk
            if size == 4:
                val_be = struct.unpack('>I', chunk)[0]
                val_le = struct.unpack('<I', chunk)[0]
                print(f"    As uint32 BE: {val_be}")
                print(f"    As uint32 LE: {val_le}")
                
                # Check if it matches known values
                if val_be == self.known_license['level']:
                    print(f"    ✓ Matches license level!")
                if val_be == self.known_license['version']:
                    print(f"    ✓ Matches version!")
                    
            elif size == 8:
                val_be = struct.unpack('>Q', chunk)[0]
                val_le = struct.unpack('<Q', chunk)[0]
                print(f"    As uint64 BE: {val_be}")
                print(f"    As uint64 LE: {val_le}")
                
                # Check if it could be software ID
                if MIKRO_AVAILABLE:
                    try:
                        sw_id_be = mikro_softwareid_encode(val_be)
                        sw_id_le = mikro_softwareid_encode(val_le)
                        print(f"    As SW ID BE: {sw_id_be}")
                        print(f"    As SW ID LE: {sw_id_le}")
                        
                        if sw_id_be == self.known_license['software_id']:
                            print(f"    ✓ Matches software ID (BE)!")
                        if sw_id_le == self.known_license['software_id']:
                            print(f"    ✓ Matches software ID (LE)!")
                    except:
                        pass
            
            offset += size
    
    def generate_valid_license(self):
        """Attempt to generate a valid license based on analysis"""
        print(f"\nGENERATING VALID LICENSE:")
        print("-" * 40)
        
        # This would require understanding the signature algorithm
        # For now, we'll show the structure we've discovered
        print("Based on analysis, a valid license would need:")
        print("1. Correct software ID encoding")
        print("2. Proper level and version fields")
        print("3. Valid nonce generation")
        print("4. Correct signature algorithm")
        print("\nTo create a valid license, we need to:")
        print("- Understand the signature verification algorithm")
        print("- Find the private key or signing method")
        print("- Determine the exact payload structure")

def main():
    """Main analysis function"""
    re = MikroTikLicenseReverseEngineer()
    license_data = re.analyze_structure()
    re.generate_valid_license()
    
    print(f"\n" + "=" * 60)
    print("REVERSE ENGINEERING COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
