# MikroTik RouterOS License Key Generator - Python Version

This is a Python implementation of the MikroTik RouterOS license key generator, converting the original binary executables to a more accessible and cross-platform Python application.

## Features

- **Cross-platform**: Works on Windows, Linux, and macOS
- **Command-line interface**: Easy to use from terminal/command prompt
- **Graphical interface**: User-friendly GUI version
- **Multiple architectures**: Replaces the need for separate ARM, ARM64, and x86 binaries
- **Flexible licensing**: Support for different license levels and features
- **Validation**: Built-in software ID validation
- **Random generation**: Can generate random software IDs for testing

## Requirements

- Python 3.6 or later
- Required Python modules (from parent directory):
  - `mikro.py`
  - `sha256.py` 
  - `toyecc/` directory with elliptic curve cryptography modules

## Installation

1. Ensure Python 3.6+ is installed on your system
2. Make sure all required modules are available in the parent directory
3. The keygen files should be in the `keygen/` subdirectory

## Usage

### Command Line Interface

#### Basic usage:
```bash
python keygen.py SOFTWARE_ID
```

#### Examples:
```bash
# Generate license for specific software ID
python keygen.py TEST-1234

# Generate with specific level and features
python keygen.py ABCD-EFGH --level 6 --features system dhcp wireless

# Generate random software ID and license
python keygen.py --random

# Validate software ID format
python keygen.py --validate TEST-1234

# Quiet mode (only output license key)
python keygen.py TEST-1234 --quiet
```

#### Available options:
- `--level, -l`: License level (1-6, default: 6)
- `--features, -f`: Features to enable
- `--random, -r`: Generate random software ID
- `--validate, -v`: Only validate software ID format
- `--quiet, -q`: Quiet mode - only output license key
- `--help, -h`: Show help message

#### Available features:
- `system`: Basic system functionality
- `dhcp`: DHCP server/client
- `wireless`: Wireless features
- `routing`: Advanced routing
- `ppp`: PPP connections
- `hotspot`: Hotspot functionality
- `mpls`: MPLS support
- `advanced-tools`: Advanced tools

### Graphical Interface

Launch the GUI version:
```bash
python keygen_gui.py
```

The GUI provides:
- Easy software ID input with random generation
- License level selection
- Feature checkboxes
- License validation
- Copy to clipboard functionality
- Clear/reset options

### Convenience Scripts

#### Windows (keygen.bat):
```cmd
keygen.bat TEST-1234
keygen.bat --random
keygen.bat --gui
```

#### Linux/macOS (keygen.sh):
```bash
./keygen.sh TEST-1234
./keygen.sh --random
./keygen.sh --gui
```

## Software ID Format

Software IDs must be in the format `XXXX-XXXX` where each character is from the set:
`TN0BYX18S5HZ4IA67DGF3LPCJQRUK9MW2VE`

Examples of valid software IDs:
- `TEST-1234`
- `ABCD-EFGH`
- `T0BY-X185`

## License Levels

1. **Level 1**: Demo/Trial
2. **Level 2**: Basic
3. **Level 3**: Standard
4. **Level 4**: Advanced
5. **Level 5**: Professional
6. **Level 6**: Unlimited (default)

## Output

The generated license key is a base64-encoded string that can be used with MikroTik RouterOS systems. Example output:

```
Software ID: TEST-1234
License Level: 6
Features: system, dhcp, wireless, routing, ppp, hotspot

License Key:
UaBcAbUUoLIlNGQHy5uwRDAAA8zZxNNuzD+LP1a0jidRoRB8Q0vWccu7jWcS8mBmZYAanTR0ETmKvkdGoMzWffN5TS1MoTNPOgkXTQ9kSD5jHXvhdm6N1OOus9b5DPtYpaYW5nbQSL5F90B5eTgxDA
```

## Files

- `keygen.py`: Main command-line interface
- `keygen_gui.py`: Graphical user interface
- `keygen.bat`: Windows convenience script
- `keygen.sh`: Linux/macOS convenience script
- `README.md`: This documentation

## Troubleshooting

### Import Errors
If you get import errors, make sure:
1. The parent directory contains `mikro.py`, `sha256.py`, and `toyecc/` directory
2. All required dependencies are installed
3. Python can find the modules (check PYTHONPATH)

### Invalid Software ID
Software IDs must:
- Be exactly 8 characters (plus dash)
- Use only characters from the MikroTik character set
- Follow the XXXX-XXXX format

### GUI Issues
For GUI problems:
- Ensure tkinter is installed (usually comes with Python)
- Try running from command line to see error messages
- Check that all dependencies are available

## Compatibility

This Python version replaces the original binary executables:
- `keygen_x86` (x86_64 Linux binary)
- `keygen_arm` (ARM Linux binary) 
- `keygen_aarch64` (ARM64 Linux binary)

The Python version provides the same functionality but with better cross-platform support and easier maintenance.

## Security Note

This tool is for educational and testing purposes. The private keys used for signing are placeholders and should be replaced with proper keys for production use.

## License

This project follows the same license as the parent MikroTik patch project.
