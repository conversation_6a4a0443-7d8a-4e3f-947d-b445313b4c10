#!/usr/bin/env python3
"""
Quick script to parse the provided MikroTik license key
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from keygen import MikroTikKeygen

def main():
    license_key = """-----BEGIN MIKROTIK SOFTWARE KEY------------
uaSG53vbUuXz9B4uP47OuzyfBwlImqD4x/PqXUbK+g4L
7pQAxT/8WN2jnU2FGKSQ5x/J64bSS9AbfbfslX3xHA==
-----END MIKROTIK SOFTWARE KEY--------------"""

    print("Parsing MikroTik License Key")
    print("=" * 50)
    print("License Key:")
    print(license_key)
    print("\n" + "=" * 50)
    
    keygen = MikroTikKeygen()
    info = keygen.parse_license_key(license_key)
    
    if info:
        print("PARSED INFORMATION:")
        print("-" * 30)
        print(f"Software ID: {info.get('software_id', 'Unknown')}")
        print(f"License Level: {info.get('level', 'Unknown')}")
        print(f"Expiry Date: {info.get('expiry_date', 'Unknown')}")
        print(f"Features: {', '.join(info.get('features', []))}")
        print(f"Feature Mask: {info.get('feature_mask', 'Unknown')}")
        print(f"Payload Length: {info.get('payload_length', 0)} bytes")
        print(f"Signature Length: {info.get('signature_length', 0)} bytes")
        print(f"Total Length: {info.get('total_length', 0)} bytes")
        
        if 'parse_error' in info:
            print(f"Parse Error: {info['parse_error']}")
        
        print(f"\nRaw Key (base64): {info.get('raw_key', 'Unknown')}")
        print(f"Payload (hex): {info.get('payload_hex', 'Unknown')}")
        
    else:
        print("Failed to parse license key")

if __name__ == "__main__":
    main()
